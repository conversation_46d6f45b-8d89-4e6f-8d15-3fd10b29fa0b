const sideBarMixIn = {
  data() {
    return {
      mask: false,
      funData: {
        cascade_list: [],
        formula: "",
        automation_list: [],
        replaceRule: [],
      },
      dataSource: [
        {
          name: "文本域",
          type: "field",
          show: false,
          children: [
            {
              name: "常规",
              type: "title",
              module: "normal",
              show: true,
              children: [
                {
                  name: "名称",
                  key: "name",
                  type: "string",
                  placeholder: "文本域名称",
                  value: "",
                  tip: "关联的字段，其中>前的为指定的数据集，如果字段在所有数据集中唯一，也可省略>及前面的数据集",
                },
                {
                  name: "固定宽度",
                  key: "fix_width",
                  type: "number",
                  value: 0,
                  event: "change",
                  tip: "固定宽度就是同时设置最大宽度与最小宽度",
                },
                {
                  name: "最大宽度",
                  key: "max_width",
                  type: "number",
                  value: 0,
                  disabled: false,
                  tip: "当设置最大宽度后，超过最大宽度后内容会被压缩，如需缩小字号请设置最大高度",
                },
                {
                  name: "最小宽度",
                  key: "min_width",
                  type: "number",
                  value: 0,
                  disabled: false,
                },

                {
                  name: "最大高度",
                  key: "maxHeight",
                  type: "number",
                  value: 0,
                  tip: "当设置最大高度后，一旦内容超出最大高度则会缩小字号以保持高度，直到缩至最小字号",
                },
                {
                  name: "背景文本",
                  key: "placeholder",
                  type: "string",
                  value: "",
                },
                {
                  name: "边框类型",
                  key: "display_type",
                  type: "select",
                  children: [
                    { name: "默认类型", type: "normal", checked: true },
                    {
                      name: "边框(不打印)",
                      type: "input",
                      checked: false,
                    },
                    {
                      name: "边框(打印)",
                      type: "printInput",
                      checked: false,
                    },
                    { name: "底线(打印)", type: "line", checked: false },
                  ],
                },
                {
                  name: "对齐方式",
                  key: "align",
                  type: "select",
                  children: [
                    { name: "左对齐", type: "left", checked: true },
                    { name: "居中对齐", type: "center", checked: false },
                    { name: "右对齐", type: "right", checked: false },
                  ],
                },
                {
                  name: "类型",
                  key: "type",
                  type: "select",
                  children: [
                    {
                      name: "常规",
                      type: "normal",
                      connect: "常规",
                      checked: true,
                    },
                    {
                      name: "标签",
                      type: "label",
                      connect: "标签",
                      checked: false,
                    },
                    {
                      name: "下拉列表",
                      type: "select",
                      checked: false,
                      connect: "下拉列表",
                    },
                    {
                      name: "日期选择器",
                      type: "date",
                      checked: false,
                      connect: "日期",
                    },
                    {
                      name: "数字",
                      type: "number",
                      checked: false,
                      connect: "数字",
                    },
                    {
                      name: "锚点",
                      type: "anchor",
                      connect: "锚点",
                      checked: false,
                    },
                  ],
                },
              ],
            },
            {
              name: "日期",
              type: "title",
              module: "date",
              show: false,
              children: [
                {
                  name: "输出格式",
                  key: "replace_format",
                  tip: "当字段值符合YYYY-MM-DD HH:mm:ss格式时输出格式生效，否则请使用展示规则功能",
                  type: "select",
                  children: [
                    { name: "YYYY-MM-DD", type: 0, checked: false },
                    { name: "YYYY-MM-DD HH:mm", type: 1, checked: false },
                    {
                      name: "YYYY-MM-DD HH:mm:ss",
                      type: 2,
                      checked: false,
                    },
                    { name: "YYYY年MM月DD日", type: 3, checked: false },
                    {
                      name: "YYYY年MM月DD日 HH时",
                      type: 4,
                      checked: false,
                    },
                    {
                      name: "YYYY年MM月DD日 HH时mm分",
                      type: 5,
                      checked: false,
                    },
                    { name: "MM-DD", type: 6, checked: false },
                    { name: "MM月DD日", type: 7, checked: false },
                    { name: "HH:mm", type: 8, checked: false },
                    { name: "HH时mm分", type: 9, checked: false },
                    {
                      name: "YYYY年MM月DD日 HH时mm分ss秒",
                      type: 10,
                      checked: false,
                    },
                    { name: "MM-DD HH:mm", type: 11, checked: false },
                  ],
                },
              ],
            },
            {
              name: "数字",
              type: "title",
              module: "number",
              show: true,
              children: [
                {
                  name: "输出格式",
                  key: "number_format",
                  type: "select",
                  children: [
                    { name: "无限制", type: 0, checked: false },
                    { name: "整数", type: 1, checked: false },
                    { name: "保留一位小数", type: 2, checked: false },
                    { name: "保留两位小数", type: 3, checked: false },
                    { name: "保留三位小数", type: 4, checked: false },
                    { name: "保留四位小数", type: 5, checked: false },
                  ],
                },
              ],
            },
            {
              name: "下拉列表",
              type: "title",
              module: "select",
              show: true,
              children: [
                {
                  name: "编辑下拉项",
                  key: "source_list",
                  type: "table",
                  module: "source_list",
                  columns: [
                    {
                      title: "选项名",
                      dataIndex: "text",
                      width: "40%",
                      scopedSlots: { customRender: "text" },
                    },
                    {
                      title: "值",
                      dataIndex: "value",
                      width: "20%",
                      scopedSlots: { customRender: "value" },
                    },
                    {
                      title: "操作",
                      dataIndex: "operation",
                      width: "20%",
                      scopedSlots: { customRender: "operation" },
                    },
                  ],
                  data: [
                    {
                      key: this.uuid(),
                      text: { type: "string", value: "" },
                      value: { type: "string", value: null },
                    },
                  ],
                },
              ],
            },
            {
              name: "扩展",
              type: "title",
              module: "extend",
              show: true,
              children: [
                {
                  name: "自动扩展",
                  key: "extend",
                  type: "select",
                  tip: "当符合以下条件时字段会自动扩展：1、数据集中有多条数据；2、字段位置在表格中",
                  children: [
                    { name: "横向扩展", type: "horizontal", checked: true },
                    { name: "纵向扩展", type: "vertical", checked: false },
                    { name: "不扩展", type: "false", checked: false },
                  ],
                },
                {
                  name: "填充限制",
                  key: "fillLimit",
                  type: "select",
                  tip: "当数据集只有一项数据时，是否允许当前文本域后边的 同 name 的文本域填充数据",
                  children: [
                    { name: "无限制", type: "unlimited", checked: true },
                    {
                      name: "停止后续填充",
                      type: "haltSubsequent",
                      checked: false,
                    },
                  ],
                },
                {
                  name: "自动合并",
                  key: "merge",
                  type: "select",
                  tip: "当该字段在数据集中存在多条数据，并且设置为自动扩展时，如果字段值相同则自动合并行",
                  children: [
                    { name: "合并", type: "true", checked: false },
                    { name: "不合并", type: "false", checked: true },
                  ],
                },
              ],
            },
            {
              name: "高级",
              type: "title",
              module: "normal",
              show: true,
              children: [
                {
                  name: "级联",
                  type: "fun",
                  module: "cascade_list",
                  fun: (e) => {
                    this.instance.openFieldAdvanced("cascade", "side", e);
                  },
                },
                {
                  name: "自动化",
                  type: "fun",
                  module: "automation_list",
                  fun: (e) => {
                    this.instance.openFieldAdvanced("fieldAuto", "side", e);
                  },
                },
                {
                  name: "公式",
                  type: "fun",
                  module: "formula",
                  fun: (e) => {
                    this.instance.openFieldAdvanced("fieldFormula", "side", e);
                  },
                },
                {
                  name: "展示规则",
                  type: "fun",
                  module: "replaceRule",
                  fun: (e) => {
                    //cascade/fieldAuto/fieldFormula/replaceRule
                    this.instance.openFieldAdvanced("replaceRule", "side", e);
                  },
                },
              ],
            },
          ],
        },
        {
          name: "单选/多选框",
          type: "checkBox",
          show: true,
          children: [
            {
              name: "常规",
              type: "title",
              module: "normal",
              show: true,
              children: [
                {
                  name: "名称",
                  placeholder: "修改名称",
                  key: "name",
                  type: "string",
                  value: "",
                  tip: "关联的字段，其中>前的为指定的数据集，如果字段在所有数据集中唯一，也可省略>及前面的数据集",
                },
                {
                  name: "选择类型",
                  key: "boxMulti",
                  type: "select",
                  children: [
                    { name: "多选", type: 1, checked: false },
                    { name: "单选", type: 0, checked: true },
                  ],
                },
                {
                  name: "选择框类型",
                  key: "choiceBoxType",
                  type: "select",
                  children: [
                    { name: "矩形", type: "checkbox", checked: true },
                    {
                      name: "矩形(无边框)",
                      type: "checkbox_rimless",
                      checked: false,
                    },
                    { name: "圆形", type: "radio", checked: false },
                  ],
                },
                {
                  name: "选项列表",
                  key: "table",
                  type: "table",
                  module: "checkBox",
                  columns: [
                    {
                      title: "选项",
                      dataIndex: "text",
                      width: "15%",
                      scopedSlots: { customRender: "text" },
                    },
                    {
                      title: "编码",
                      dataIndex: "name",
                      width: "15%",
                      scopedSlots: { customRender: "name" },
                    },
                    {
                      title: "值",
                      dataIndex: "value",
                      width: "10%",
                      scopedSlots: { customRender: "value" },
                    },
                    {
                      title: "操作",
                      dataIndex: "operation",
                      width: "10%",
                      scopedSlots: { customRender: "operation" },
                    },
                  ],
                  data: [
                    {
                      key: this.uuid(),
                      text: { type: "string", value: "" },
                      name: { type: "string", value: "" },
                      value: { type: "string", value: null },
                    },
                  ],
                },
              ],
            },
            {
              name: "扩展",
              type: "title",
              module: "extend",
              show: true,
              children: [
                {
                  name: "动态替换",
                  key: "dynamic",
                  type: "select",
                  tip: "此属性与字典数据集搭配使用，当设置动态替换后，打印时会根据新的字典数据集动态替换选项内容",
                  children: [
                    { name: "替换", type: "true", checked: false },
                    { name: "不替换", type: "false", checked: true },
                  ],
                },
                {
                  name: "选中方式",
                  key: "selectType",
                  type: "select",
                  tip: "传入的字段值与选中方式设置的对应时，选项会自动设置选中",
                  children: [
                    { name: "根据值", type: "0", checked: true },
                    { name: "根据编码", type: "1", checked: false },
                  ],
                },
              ],
            },
          ],
        },
        {
          name: "分组",
          type: "group",
          show: true,
          children: [
            {
              name: "常规",
              type: "title",
              module: "normal",
              show: true,
              children: [
                {
                  name: "关联数据集",
                  placeholder: "关联数据集",
                  tip:
                    "当设置关联数据集后，如果传入的数据集不存在或者为空则自动删除对应的分组，如果数据集存在则可结合扩展属性-是否重复使用，" +
                    "还可支持续打分组功能，当传入的数据集中存在绑定数据集名称带有后缀“_print”时，则只会打印此部分内容。",
                  key: "name",
                  type: "select",
                  default: "",
                  children: [{ name: "", type: "dataset", checked: true }],
                },
                {
                  name: "开启分页",
                  key: "new_page",
                  type: "select",
                  children: [
                    { name: "开启", type: "true", checked: false },
                    { name: "关闭", type: "false", checked: true },
                  ],
                },
              ],
            },
            {
              name: "扩展",
              type: "title",
              module: "extend",
              show: true,
              children: [
                {
                  name: "是否重复",
                  key: "repeat",
                  type: "select",
                  tip: "当分组绑定的数据集如果是个数组时，设置重复后会按照数组长度自动复制分组",
                  children: [
                    { name: "重复", type: "true", checked: true },
                    { name: "不重复", type: "false", checked: false },
                  ],
                },
              ],
            },
          ],
        },
        {
          name: "单元格",
          type: "cell",
          show: true,
          children: [
            {
              name: "常规",
              type: "title",
              module: "normal",
              show: true,
              children: [
                {
                  name: "上边距",
                  key: "padding_top",
                  type: "number",
                  value: 0,
                },
                {
                  name: "下边距",
                  key: "padding_bottom",
                  type: "number",
                  value: 0,
                },
                {
                  name: "左边距",
                  key: "padding_left",
                  type: "number",
                  value: 0,
                },
                {
                  name: "右边距",
                  key: "padding_right",
                  type: "number",
                  value: 0,
                },
                {
                  name: "单元格高度",
                  key: "cell_height",
                  type: "number",
                  tip: "设置后单元格高度会固定，当内容超出单元格区域后会被裁剪",
                  value: 0,
                },
                {
                  name: "不换行模式",
                  key: "noWrap",
                  tip: "开启后段落内容将不会换行，内容超出行宽时文本会被压缩。（多段内容时可能会不符合预期效果）",
                  type: "select",
                  children: [
                    { name: "开启", type: "true", checked: false },
                    { name: "关闭", type: "false", checked: true },
                  ],
                },
              ],
            },
          ],
        },
        {
          name: "表格",
          type: "table",
          show: true,
          children: [
            {
              name: "常规",
              type: "title",
              module: "normal",
              show: true,
              children: [
                {
                  name: "开启分页",
                  key: "newPage",
                  type: "select",
                  children: [
                    { name: "开启", type: "true", checked: false },
                    { name: "关闭", type: "false", checked: true },
                  ],
                },
                {
                  name: "撑满一页",
                  key: "fullPage",
                  type: "select",
                  children: [
                    { name: "开启", type: "true", checked: false },
                    { name: "关闭", type: "false", checked: true },
                  ],
                },
              ],
            },
          ],
        },
      ],
      params: {
        field: {
          name: "",
          max_width: 0,
          min_width: 0,
          maxHeight: 0,
          placeholder: "",
          display_type: "normal",
          align: "left",
          type: "normal",
          merge: false,
          extend: "horizontal",
          meta: { merge: false, extend: "horizontal" },
          replace_format: 0,
          number_format: 0,
          source_list: [],
          fillLimit: "unlimited",
        },
        checkBox: {
          edit: true,
          choicedField: {},
          isDeleteAble: false,
          groupName: "",
          isHideSymbol: false,
          position: 0, // 选择框位置
          type: "plural",
          datasource: [],
          automation_list: [],
          isToBeGroup: 1,
          choiceBoxType: "checkbox",
        },
        group: {
          name: "",
          new_page: false,
          repeat: false,
          meta: {
            repeat: false,
          },
        },
        cell: {
          padding_left: 0,
          padding_right: 0,
          padding_top: 0,
          padding_bottom: 0,
          noWrap: false,
          cell_height: 0,
        },
        table: {
          newPage: false,
          fullPage: false,
        },
      },
    };
  },
  mounted() {},
  methods: {
    uuid() {
      const uuid = "xxxxxxxx".replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0;
        const v = c === "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
      return uuid;
    },
    updateSideData() {
      this.focusElement = this.instance.editor.focusElement;
      for (let i = 0; i < this.showRightList.length; i++) {
        const item = this.showRightList[i];
        for (const key in this.focusElement) {
          let type = key;
          if (key === "field" && this.focusElement[key].type === "box") {
            type = "checkBox";
          }

          if (item.key === type) {
            item.show = true;
            break;
          } else {
            item.show = false;
          }
        }
      }
      if (this.showRightList[0].key === "dictionary") {
        this.showRightList[0].show = true;
      }
      const result = this.showRightList.find(
        (e) => e.show && e.key === this.clickListName
      );
      if (!result) {
        this.mask = true;
      } else {
        this.mask = false;
      }
    },
    updateSideDataSource() {
      for (const key in this.focusElement) {
        let type = key;
        let ele = this.focusElement[key];
        if (key === "field" && ele.type === "box") {
          type = "checkBox";
        }
        for (let i = 0; i < this.dataSource.length; i++) {
          const data = this.dataSource[i];
          if (data.type === type) {
            if (type === "checkBox") {
              ele = this.focusElement[key].parent_box;
            }
            for (let j = 0; j < data.children.length; j++) {
              const child1 = data.children[j];
              if (child1.type === "groupRadio") {
                for (let k = 0; k < child1.children.length; k++) {
                  const child2 = child1.children[k];
                  this.updateSideDataChild(child2, ele);
                }
              } else if (child1.type === "title") {
                this.updateSideDataChild(child1, ele);
              }
            }
          }
        }
      }
      this.dataSource = [...this.dataSource];
    },
    updateSideDataChild(data, ele) {
      if (data.type === "title") {
        if (
          data.module &&
          data.module === ele.type &&
          data.module !== "normal" &&
          data.module !== "extend"
        ) {
          data.show = true;
        } else {
          if (data.module === "normal" || data.module === "extend") {
            data.show = true;
          } else {
            data.show = false;
          }
        }
        for (let m = 0; m < data.children.length; m++) {
          const child3 = data.children[m];

          if (child3.key) {
            if (ele[child3.key] !== undefined) {
              const eleData = ele[child3.key];
              if (child3.type === "string") {
                child3.value = eleData;
              } else if (child3.type === "number") {
                if (parseInt(eleData)) {
                  child3.value = parseInt(eleData);
                } else {
                  if (ele[child3.key] && ele[child3.key].height) {
                    child3.value = ele[child3.key].height;
                  } else {
                    child3.value = 0;
                  }
                }
              } else if (child3.type === "select") {
                if (child3.name === "关联数据集") {
                  child3.default = ele.name ? ele.name : " ";
                  child3.children = [];
                  const dataSets = this.dataSetInfo.dataSets.filter(
                    (m) =>
                      !/_print/.test(m.name) &&
                      m.name !== "系统变量" &&
                      m.name !== "内置变量"
                  );
                  child3.children.push({
                    name: "无关联",
                    type: "",
                    checked: false,
                  });
                  for (let k = 0; k < dataSets.length; k++) {
                    const dataSet = dataSets[k];
                    child3.children.push({
                      name: dataSet.name,
                      type: dataSet.name,
                      checked: child3.default === dataSet.name,
                    });
                  }
                } else {
                  if (child3.children[0].type === "true") {
                    if (eleData) {
                      child3.children[0].checked = true;
                      child3.children[1].checked = false;
                    } else {
                      child3.children[0].checked = false;
                      child3.children[1].checked = true;
                    }
                  } else {
                    for (let k = 0; k < child3.children.length; k++) {
                      const child4 = child3.children[k];
                      if (child4.type === eleData) {
                        child4.checked = true;
                      } else {
                        child4.checked = false;
                      }
                    }
                  }
                }
              } else if (child3.type === "table") {
                if (ele[child3.module] && ele[child3.module].length) {
                  child3.data = [];
                  for (let n = 0; n < ele[child3.module].length; n++) {
                    const listData = ele[child3.module][n];
                    const obj = {};
                    obj.key = this.uuid();
                    for (let h = 0; h < child3.columns.length - 1; h++) {
                      const type = child3.columns[h].dataIndex;
                      obj[type] = {
                        type: "string",
                        value: listData[type],
                      };
                    }
                    child3.data.push(obj);
                  }
                }
              }
            } else {
              if (child3.type === "select") {
                if (ele.meta[child3.key] !== undefined) {
                  const data = ele.meta[child3.key];
                  if (data === true) {
                    child3.children[0].checked = true;
                    child3.children[1].checked = false;
                  } else if (data === false) {
                    child3.children[0].checked = false;
                    child3.children[1].checked = true;
                  } else if (data === 0) {
                    child3.children[0].checked = true;
                    child3.children[1].checked = false;
                  } else if (data === 1) {
                    child3.children[0].checked = false;
                    child3.children[1].checked = true;
                  } else if (data === "haltSubsequent") {
                    child3.children[0].checked = false;
                    child3.children[1].checked = true;
                  } else if (data === "unlimited") {
                    child3.children[0].checked = true;
                    child3.children[1].checked = false;
                  } else if (data === "horizontal") {
                    child3.children[0].checked = true;
                    child3.children[1].checked = false;
                    child3.children[2].checked = false;
                  } else if (data === "vertical") {
                    child3.children[0].checked = false;
                    child3.children[1].checked = true;
                    child3.children[2].checked = false;
                  }
                } else {
                  if (child3.key === "fillLimit") {
                    child3.children[0].checked = true;
                    child3.children[1].checked = false;
                  } else if (child3.key === "extend") {
                    child3.children[0].checked = true;
                    child3.children[1].checked = false;
                    child3.children[2].checked = false;
                  } else if (child3.key === "merge") {
                    child3.children[0].checked = true;
                    child3.children[1].checked = false;
                  } else if (
                    ele.type === "box" &&
                    child3.children[0].type !== "true" &&
                    child3.children[0].type !== "0"
                  ) {
                    this.checkBoxDeal(child3, ele);
                  }
                }
              } else if (child3.type === "table") {
                if (ele.type === "box") {
                  child3.data = [];
                  for (let h = 0; h < ele.children.length; h++) {
                    const child4 = ele.children[h];
                    if (child4.type === "box") {
                      const cascade = ele.cascade_list.find(
                        (e) => e.text === child4.text
                      );
                      child3.data.push({
                        key: this.uuid(),
                        name: { type: "string", value: child4.name },
                        field_names: cascade.show_field_names.join(","),
                        text: { type: "string", value: child4.text },
                        value: { type: "string", value: child4.formula_value },
                      });
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    checkBoxDeal(child3, ele) {
      const boxField = ele.children[0];
      for (let h = 0; h < child3.children.length; h++) {
        const child4 = child3.children[h];
        child4.checked = false;
        if (child3.key === "boxMulti") {
          if (ele.box_multi) {
            if (child4.type === 1) {
              child4.checked = true;
            }
          } else {
            if (child4.type === 0) {
              child4.checked = true;
            }
          }
        } else if (child3.key === "choiceBoxType") {
          const widget = boxField.box_widget;
          if (widget) {
            if (widget.widgetType === "checkbox") {
              if (widget.border === "dotted") {
                if (child4.type === "checkbox_rimless") {
                  child4.checked = true;
                }
              } else {
                if (child4.type === "checkbox") {
                  child4.checked = true;
                }
              }
            } else if (widget.widgetType === "radio") {
              if (child4.type === "radio") {
                child4.checked = true;
              }
            }
          }
        }
      }
    },
    sideSubmit() {
      if (this.clickListName === "checkBox") {
        this.checkBoxData(this.clickListName);
      } else {
        this.translateData(this.clickListName);
      }
      this.$editor.info("修改成功");
    },
    checkBoxData(key) {
      const data = this.dataSource.find((e) => e.type === key);
      let param = this.params[key];
      for (let i = 0; i < data.children.length; i++) {
        const child1 = data.children[i];
        if (child1.type === "title") {
          for (let j = 0; j < child1.children.length; j++) {
            const child2 = child1.children[j];
            if (child2.key === "name") {
              param.groupName = child2.value;
            } else if (child2.key === "boxMulti") {
              const type = child2.children.find((e) => e.checked);
              param.type = type.type ? "plural" : "single";
            } else if (child2.key === "choiceBoxType") {
              const type = child2.children.find((e) => e.checked);
              param.choiceBoxType = type.type;
            } else if (child2.key === "table") {
              param.datasource = [];
              for (let k = 0; k < child2.data.length; k++) {
                const child3 = child2.data[k];
                param.datasource.push({
                  key: this.uuid(),
                  name:
                    child3.name && child3.name.value ? child3.name.value : "",
                  value: child3.text.value,
                  formula_value: child3.value.value,
                  field_names: child3.field_names,
                  disabled: 0,
                });
              }
            } else if (child2.key === "dynamic") {
              const type = child2.children.find((e) => e.checked);
              param.dynamic = JSON.parse(type.type);
            } else if (child2.key === "selectType") {
              const type = child2.children.find((e) => e.checked);
              param.selectType = JSON.parse(type.type);
            }
          }
        }
      }

      const element = this.instance.editor.focusElement["field"];
      if (!element) {
        this.$editor.info("请重新点击需要修改的内容");
      } else {
        if (element.parent_box) {
          param.choicedField = element;
          param.isDeleteAble = element.deletable;
          param.isHideSymbol = element.start_symbol ? 0 : 1;
          param.position = element.position;
          param.automation_list = element.automation_list;
          const field = this.instance.insertChioceModal(param);
          this.instance.editor.locatePathInField(field);
          this.instance.editor.updateCaret();
        }
      }
    },

    translateData(key) {
      const data = this.dataSource.find((e) => e.type === key);
      let param = this.params[key];
      if (!data) return;
      const element = { ...this.instance.editor.focusElement }[key];
      for (let i = 0; i < data.children.length; i++) {
        const child1 = data.children[i];
        if (child1.type === "title") {
          for (let j = 0; j < child1.children.length; j++) {
            const child2 = child1.children[j];
            for (const paramKey in param) {
              if (child2.key === paramKey) {
                if (child2.type === "string" || child2.type === "number") {
                  if (child2.value === "true" || child2.value === "false") {
                    param[paramKey] = JSON.parse(child2.value);
                  } else {
                    param[paramKey] = child2.value;
                  }
                } else if (child2.type === "select") {
                  for (let k = 0; k < child2.children.length; k++) {
                    const child3 = child2.children[k];
                    if (child3.checked) {
                      let handleVal = child3.type;
                      if (handleVal === "true" || handleVal === "false") {
                        handleVal = JSON.parse(handleVal);
                      }
                      if (
                        child2.key === "merge" ||
                        child2.key === "repeat" ||
                        child2.key === "extend"
                      ) {
                        param.meta[paramKey] = handleVal;
                      } else if (child2.key === "fillLimit") {
                        param.meta[paramKey] = handleVal;
                      } else {
                        param[paramKey] = handleVal;
                      }
                      break;
                    }
                  }
                } else if (child2.type === "table") {
                  const para = [];
                  for (let m = 0; m < child2.data.length; m++) {
                    const childData = child2.data[m];
                    para.push({
                      code: "",
                      text: childData.text.value,
                      value: childData.value.value,
                    });
                  }
                  param[paramKey] = [...para];
                }
              }
            }
          }
        }
      }
      if (key === "cell") {
        this.instance.editor.updateCellsAttr({ attr: param });
      } else {
        for (const key in element) {
          if (Object.prototype.hasOwnProperty.call(param, key)) {
            element[key] = param[key];
          }
        }
        this.instance.editor.refreshDocument(true);
      }
    },
  },
};

export default sideBarMixIn;
